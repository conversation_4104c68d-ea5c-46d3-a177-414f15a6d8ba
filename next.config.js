// Removed PWA configuration for better performance

/** @type {import('next').NextConfig} */
const withBundleAnalyzer = require("@next/bundle-analyzer")({
  enabled: process.env.ANALYZE === "true",
});

const nextConfig = {
  reactStrictMode: true,

  // Add headers to allow social media embeds
  async headers() {
    return [
      {
        source: "/:path*",
        headers: [
          {
            key: "Content-Security-Policy",
            value: [
              "frame-src 'self'",
              "https://www.youtube-nocookie.com",
              "https://www.youtube.com",
              "https://platform.twitter.com",
              "https://syndication.twitter.com",
              "https://www.facebook.com",
              "https://web.facebook.com",
              "https://www.instagram.com",
              "https://instagram.com",
              "https://connect.facebook.net",
              "https://staticxx.facebook.com",
              "https://scontent.cdninstagram.com",
              "https://scontent-*.cdninstagram.com",
              "https://scontent.xx.fbcdn.net",
              "https://scontent-*.xx.fbcdn.net;",
              "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
              "https://platform.twitter.com",
              "https://connect.facebook.net",
              "https://www.instagram.com",
              "https://instagram.com;",
              "connect-src 'self'",
              "https://syndication.twitter.com",
              "https://api.twitter.com",
              "https://graph.facebook.com",
              "https://www.instagram.com",
              "https://instagram.com;",
            ].join(" "),
          },
        ],
      },
    ];
  },

  // Image configuration with optimizations
  images: {
    domains: [
      "media.graphassets.com",
      "ap-south-1.graphassets.com",
      "png.pngtree.com",
      "e7.pngegg.com",
      "ap-south-1.cdn.hygraph.com", // Add Hygraph CDN domain
      "via.placeholder.com", // Add placeholder.com for fallback images
      "media.graphcms.com", // Add GraphCMS domain (old name for Hygraph)
      "media.hygraph.com", // Add Hygraph media domain
      "images.unsplash.com", // Add Unsplash for stock images
      "res.cloudinary.com", // Add Cloudinary for image hosting
    ], // Allowed image domains
    formats: ["image/avif", "image/webp"],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920],
    imageSizes: [16, 32, 48, 64, 96, 128, 256],
    minimumCacheTTL: 172800, // Increase cache time to 48 hours for better performance
    dangerouslyAllowSVG: true,
    contentDispositionType: "attachment",
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    // Optimize image loading
    loader: "default",
    path: "/_next/image",
    disableStaticImages: false,
    // Enable remote patterns for more flexible image sources
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**.hygraph.com",
        pathname: "**",
      },
      {
        protocol: "https",
        hostname: "**.graphassets.com",
        pathname: "**",
      },
      {
        protocol: "https",
        hostname: "**.graphcms.com",
        pathname: "**",
      },
      {
        protocol: "https",
        hostname: "via.placeholder.com",
        pathname: "**",
      },
      {
        protocol: "https",
        hostname: "images.unsplash.com",
        pathname: "**",
      },
      {
        protocol: "https",
        hostname: "res.cloudinary.com",
        pathname: "**",
      },
    ],
    // Enable image optimization in production
    // This will help with image loading and performance
    unoptimized: false,
  },

  // Rewrites configuration
  async rewrites() {
    return [
      {
        source: "/robots.txt",
        destination: "/robots.txt",
      },
      {
        source: "/sitemap.xml",
        destination: "/api/sitemap",
      },
      // This rewrite maintains backward compatibility for the sitemap-news.xml
      // It allows search engines to continue accessing the sitemap at the original URL
      // while the content is dynamically generated by our API route
      {
        source: "/sitemap-news.xml",
        destination: "/api/sitemap-news",
      },
      {
        source: "/blog",
        destination: "/",
      },
    ];
  },

  // Redirects configuration for SEO
  async redirects() {
    return [
      // Redirect trailing slashes
      {
        source: "/:path+/",
        destination: "/:path+",
        permanent: true,
      },
      // Redirect lowercase URLs
      {
        source: "/:path*/[A-Z]:rest*",
        destination: "/:path*/:rest*",
        permanent: true,
      },
    ];
  },

  // Webpack configuration for audio files and optimizations
  webpack(config) {
    // Audio file handling
    config.module.rules.push({
      test: /\.(mp3|wav)$/,
      use: {
        loader: "file-loader",
        options: {
          name: "[path][name].[ext]",
        },
      },
    });

    return config;
  },

  // Performance optimizations
  swcMinify: true,
  compiler: {
    removeConsole:
      process.env.NODE_ENV === "production"
        ? {
            exclude: ["error", "warn"],
          }
        : false,
  },

  // Optimize production builds
  productionBrowserSourceMaps: false,

  // Performance optimizations
  compress: true,
  poweredByHeader: false,

  // Experimental features for better performance
  experimental: {
    scrollRestoration: true,
    // optimizeCss: true, // Disabled - requires critters package
  },

  // Removed experimental features that were causing issues
};

module.exports = withBundleAnalyzer(nextConfig);
