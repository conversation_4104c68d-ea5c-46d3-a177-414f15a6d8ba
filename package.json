{"private": true, "scripts": {"dev": "next dev", "build": "next build", "build:analyze": "ANALYZE=true next build", "postbuild": "next-sitemap --config next-sitemap.config.js", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test:build": "npm run build && npm run start", "audit:security": "npm audit --audit-level moderate", "audit:deps": "npm outdated", "analyze": "ANALYZE=true npm run build", "performance": "node scripts/analyze-performance.js", "lighthouse": "lighthouse http://localhost:3000 --output html --output-path ./lighthouse-report.html"}, "dependencies": {"@apollo/client": "^3.13.8", "@fortawesome/fontawesome-free": "^6.5.2", "@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-brands-svg-icons": "^6.5.2", "@fortawesome/free-regular-svg-icons": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/react-fontawesome": "^0.2.2", "@graphcms/rich-text-react-renderer": "^0.6.2", "@graphcms/rich-text-types": "^0.5.1", "@next/third-parties": "^14.1.0", "@tailwindcss/typography": "^0.5.16", "axios": "^1.6.8", "chart.js": "^4.4.3", "dotenv": "^16.3.1", "embla-carousel": "^8.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "firebase": "^10.11.1", "framer-motion": "^11.2.10", "google-news-sitemap": "^1.0.10", "graphql": "^16.8.1", "graphql-request": "^6.1.0", "html-react-parser": "^5.1.8", "moment": "^2.30.1", "next": "^14.2.28", "next-seo": "^6.5.0", "next-sitemap": "^4.2.3", "prismjs": "^1.30.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-icons": "^5.2.1", "react-infinite-scroll-component": "^6.1.0", "react-lazy-load-image-component": "^1.6.0", "react-lite-youtube-embed": "^2.5.1", "react-multi-carousel": "^2.8.4", "react-player": "^2.15.1", "react-responsive": "^10.0.0", "react-social-media-embed": "^2.5.18", "react-spinners": "^0.13.8", "react-syntax-highlighter": "^15.6.1", "react-twitter-embed": "^4.0.4", "reading-time": "^1.5.0", "sass": "^1.72.0", "sharp": "^0.34.1", "validator": "^13.11.0", "web-vitals": "^3.5.2"}, "devDependencies": {"@graphql-codegen/cli": "^5.0.6", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.1", "@next/bundle-analyzer": "^15.5.2", "@types/node": "^20.11.30", "@types/react": "^18.2.67", "@types/react-dom": "^18.2.22", "autoprefixer": "^10.4.19", "file-loader": "^6.2.0", "postcss": "^8.4.38", "puppeteer": "^24.19.0", "tailwindcss": "^3.4.1", "typescript": "5.8.3", "webpack-bundle-analyzer": "^4.10.2"}}